<?php
/**
 * Main Entry Point - IPTV XUI One Content Manager
 * ==============================================
 * 
 * Root index file for hosting compatibility
 * Redirects to the main application in public/ directory
 */

// Start session
session_start();

// Error reporting for development (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if we're accessing the root or a specific page
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';

// Remove script name from request URI to get the path
$path = str_replace(dirname($scriptName), '', $requestUri);
$path = trim($path, '/');

// If accessing root, show the main application
if (empty($path) || $path === 'index.php') {
    // Include the main application
    require_once __DIR__ . '/public/index.php';
    exit;
}

// Handle API requests
if (strpos($path, 'api/') === 0) {
    $apiFile = str_replace('api/', '', $path);
    $apiPath = __DIR__ . '/api/' . $apiFile;
    
    if (file_exists($apiPath)) {
        require_once $apiPath;
        exit;
    } else {
        http_response_code(404);
        header('Content-Type: application/json');
        echo json_encode(['error' => 'API endpoint not found']);
        exit;
    }
}

// Handle asset requests (CSS, JS, images)
if (strpos($path, 'assets/') === 0) {
    $assetFile = str_replace('assets/', '', $path);
    $assetPath = __DIR__ . '/public/assets/' . $assetFile;
    
    if (file_exists($assetPath)) {
        // Set appropriate content type
        $extension = pathinfo($assetPath, PATHINFO_EXTENSION);
        $contentTypes = [
            'css' => 'text/css',
            'js' => 'application/javascript',
            'png' => 'image/png',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'gif' => 'image/gif',
            'svg' => 'image/svg+xml',
            'ico' => 'image/x-icon'
        ];
        
        if (isset($contentTypes[$extension])) {
            header('Content-Type: ' . $contentTypes[$extension]);
        }
        
        // Set cache headers for assets
        header('Cache-Control: public, max-age=31536000'); // 1 year
        header('Expires: ' . gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        
        readfile($assetPath);
        exit;
    }
}

// If nothing matches, show the main application
require_once __DIR__ . '/public/index.php';
?>
