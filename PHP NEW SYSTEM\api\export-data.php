<?php
/**
 * Export Data API Endpoint
 * ========================
 * 
 * Exports content in various formats (M3U, TXT, JSON, CSV)
 */

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../core/ContentManager.php';
require_once __DIR__ . '/../config/settings.php';

try {
    $contentManager = new ContentManager();
    
    // Get export parameters
    $format = $_GET['format'] ?? 'json';
    $filters = [
        'limit' => (int)($_GET['limit'] ?? 1000),
        'offset' => (int)($_GET['offset'] ?? 0),
        'search' => $_GET['search'] ?? '',
        'category' => $_GET['category'] ?? '',
        'missing_tmdb' => isset($_GET['missing_tmdb']) && $_GET['missing_tmdb'] === 'true',
        '4k_content' => isset($_GET['4k_content']) && $_GET['4k_content'] === 'true',
        '60fps_content' => isset($_GET['60fps_content']) && $_GET['60fps_content'] === 'true',
        'symlink_content' => isset($_GET['symlink_content']) && $_GET['symlink_content'] === 'true',
        'direct_content' => isset($_GET['direct_content']) && $_GET['direct_content'] === 'true'
    ];
    
    $options = [
        'include_metadata' => isset($_GET['include_metadata']) && $_GET['include_metadata'] === 'true',
        'compress' => isset($_GET['compress']) && $_GET['compress'] === 'true'
    ];
    
    // Validate format
    $allowedFormats = ['m3u', 'txt', 'json', 'csv'];
    if (!in_array($format, $allowedFormats)) {
        throw new Exception('Invalid export format');
    }
    
    // Export content
    $result = $contentManager->exportContent($format, $filters, $options);
    
    if (!$result['success']) {
        throw new Exception($result['error']);
    }
    
    // Set appropriate headers for download
    $filename = $result['filename'];
    $mimeType = $result['mime_type'];
    $content = $result['content'];
    
    // If this is a download request, send file
    if (isset($_GET['download']) && $_GET['download'] === 'true') {
        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($content));
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
        
        echo $content;
        exit;
    }
    
    // Otherwise, return JSON response with content info
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'export' => [
            'format' => $format,
            'filename' => $filename,
            'size' => strlen($content),
            'mime_type' => $mimeType,
            'content_preview' => substr($content, 0, 500) . (strlen($content) > 500 ? '...' : ''),
            'download_url' => $_SERVER['REQUEST_URI'] . '&download=true'
        ],
        'filters_applied' => $filters,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
