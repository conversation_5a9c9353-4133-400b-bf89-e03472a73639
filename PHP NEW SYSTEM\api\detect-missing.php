<?php
/**
 * Detect Missing TMDB Content API Endpoint
 * ========================================
 *
 * Identifies content missing TMDB metadata
 */

// Initialize session management
require_once __DIR__ . '/../config/session.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once __DIR__ . '/../core/DatabaseManager.php';
require_once __DIR__ . '/../core/ContentManager.php';

try {
    $db = new DatabaseManager();
    $contentManager = new ContentManager();
    
    // Get parameters
    $limit = (int)($_GET['limit'] ?? 100);
    $offset = (int)($_GET['offset'] ?? 0);
    $action = $_GET['action'] ?? 'detect';
    
    switch ($action) {
        case 'detect':
            // Get content missing TMDB data
            $missingContent = $db->getMissingTMDBContent($limit, $offset);
            
            // Analyze the missing content
            $analysis = [
                'total_missing' => count($missingContent),
                'by_type' => [],
                'by_year' => [],
                'recommendations' => []
            ];
            
            foreach ($missingContent as $item) {
                // Count by type
                $type = 'movie'; // Default, could be enhanced with better type detection
                $analysis['by_type'][$type] = ($analysis['by_type'][$type] ?? 0) + 1;
                
                // Count by year
                $year = $item['year'] ?? 'unknown';
                $analysis['by_year'][$year] = ($analysis['by_year'][$year] ?? 0) + 1;
            }
            
            // Generate recommendations
            if (count($missingContent) > 50) {
                $analysis['recommendations'][] = [
                    'type' => 'batch_processing',
                    'message' => 'Consider processing in smaller batches for better performance',
                    'priority' => 'medium'
                ];
            }
            
            if ($analysis['by_year']['unknown'] ?? 0 > 10) {
                $analysis['recommendations'][] = [
                    'type' => 'year_detection',
                    'message' => 'Many items missing year information - consider improving title parsing',
                    'priority' => 'low'
                ];
            }
            
            echo json_encode([
                'success' => true,
                'data' => $missingContent,
                'analysis' => $analysis,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'has_more' => count($missingContent) === $limit
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'enrich':
            // Start TMDB enrichment process
            $enrichLimit = (int)($_POST['limit'] ?? 50);
            
            $result = $contentManager->enrichMissingTMDBContent($enrichLimit, function($progress, $current, $total) {
                // Progress callback - in a real implementation, this could use WebSockets
                // or server-sent events for real-time updates
            });
            
            echo json_encode([
                'success' => $result['success'],
                'stats' => $result['stats'] ?? [],
                'message' => $result['message'] ?? '',
                'error' => $result['error'] ?? null,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'preview':
            // Preview what would be enriched
            $previewLimit = min((int)($_GET['preview_limit'] ?? 10), 20);
            $missingContent = $db->getMissingTMDBContent($previewLimit, 0);
            
            $preview = [];
            foreach ($missingContent as $item) {
                $preview[] = [
                    'id' => $item['id'],
                    'title' => $item['stream_display_name'],
                    'current_tmdb_id' => $item['tmdb_id'],
                    'year' => $item['year'],
                    'category' => $item['category_name'] ?? 'Uncategorized',
                    'estimated_match_confidence' => 'medium' // Could be enhanced with actual matching
                ];
            }
            
            echo json_encode([
                'success' => true,
                'preview' => $preview,
                'total_missing' => count($missingContent),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'stats':
            // Get statistics about missing content
            $contentStats = $db->getContentStats();
            $totalContent = $contentStats['total_streams'] ?? 0;
            $missingCount = count($db->getMissingTMDBContent(1000, 0)); // Get a larger sample
            
            $stats = [
                'total_content' => $totalContent,
                'missing_tmdb' => $missingCount,
                'completion_rate' => $totalContent > 0 ? (($totalContent - $missingCount) / $totalContent) * 100 : 0,
                'priority_missing' => [
                    '4k_content' => 0, // Would need specific queries
                    '60fps_content' => 0,
                    'recent_additions' => 0
                ]
            ];
            
            echo json_encode([
                'success' => true,
                'stats' => $stats,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        default:
            throw new Exception('Invalid action specified');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
}
?>
