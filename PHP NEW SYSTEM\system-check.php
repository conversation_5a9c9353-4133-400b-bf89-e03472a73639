<?php
/**
 * System Check - IPTV XUI One Content Manager
 * ==========================================
 *
 * Verifies that the system is properly configured and ready to run
 */

// Initialize session management
require_once __DIR__ . '/config/session.php';

// Include hosting configuration
require_once __DIR__ . '/config/hosting.php';

// Initialize system check
$systemCheck = [
    'php' => [],
    'extensions' => [],
    'permissions' => [],
    'configuration' => [],
    'database' => [],
    'overall_status' => 'unknown'
];

// Check PHP version
$systemCheck['php']['version'] = PHP_VERSION;
$systemCheck['php']['version_ok'] = version_compare(PHP_VERSION, '8.0.0', '>=');

// Check PHP settings
$systemCheck['php']['memory_limit'] = ini_get('memory_limit');
$systemCheck['php']['upload_max_filesize'] = ini_get('upload_max_filesize');
$systemCheck['php']['post_max_size'] = ini_get('post_max_size');
$systemCheck['php']['max_execution_time'] = ini_get('max_execution_time');

// Check required extensions
$requiredExtensions = ['pdo', 'pdo_mysql', 'json', 'mbstring', 'curl', 'fileinfo'];
foreach ($requiredExtensions as $ext) {
    $systemCheck['extensions'][$ext] = extension_loaded($ext);
}

// Check file permissions
$systemCheck['permissions'] = checkWritePermissions();

// Check configuration files
$configFiles = [
    'settings.php' => __DIR__ . '/config/settings.php',
    'database.php' => __DIR__ . '/config/database.php',
    'tmdb_config.php' => __DIR__ . '/config/tmdb_config.php'
];

foreach ($configFiles as $name => $path) {
    $systemCheck['configuration'][$name] = [
        'exists' => file_exists($path),
        'readable' => is_readable($path)
    ];
}

// Test database connection
try {
    require_once __DIR__ . '/config/database.php';
    require_once __DIR__ . '/core/DatabaseManager.php';
    
    $db = new DatabaseManager();
    $systemCheck['database']['connection'] = $db->testConnection();
    $systemCheck['database']['error'] = null;
} catch (Exception $e) {
    $systemCheck['database']['connection'] = false;
    $systemCheck['database']['error'] = $e->getMessage();
}

// Determine overall status
$allPhpOk = $systemCheck['php']['version_ok'];
$allExtensionsOk = !in_array(false, $systemCheck['extensions'], true);
$allPermissionsOk = true;
foreach ($systemCheck['permissions'] as $perm) {
    if (!$perm['writable']) {
        $allPermissionsOk = false;
        break;
    }
}
$allConfigOk = true;
foreach ($systemCheck['configuration'] as $config) {
    if (!$config['exists'] || !$config['readable']) {
        $allConfigOk = false;
        break;
    }
}

if ($allPhpOk && $allExtensionsOk && $allPermissionsOk && $allConfigOk && $systemCheck['database']['connection']) {
    $systemCheck['overall_status'] = 'excellent';
} elseif ($allPhpOk && $allExtensionsOk && $allConfigOk) {
    $systemCheck['overall_status'] = 'good';
} elseif ($allPhpOk && $allExtensionsOk) {
    $systemCheck['overall_status'] = 'fair';
} else {
    $systemCheck['overall_status'] = 'poor';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Check - IPTV XUI One Content Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #0f172a;
            color: #f1f5f9;
            margin: 0;
            padding: 2rem;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        .status-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.875rem;
        }
        .status-excellent { background: #10b981; color: white; }
        .status-good { background: #3b82f6; color: white; }
        .status-fair { background: #f59e0b; color: white; }
        .status-poor { background: #ef4444; color: white; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 1rem;
            padding: 2rem;
        }
        .card h3 {
            margin-top: 0;
            color: #3b82f6;
            border-bottom: 1px solid #334155;
            padding-bottom: 1rem;
        }
        .check-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #334155;
        }
        .check-item:last-child {
            border-bottom: none;
        }
        .check-status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .status-ok { background: #10b981; color: white; }
        .status-error { background: #ef4444; color: white; }
        .status-warning { background: #f59e0b; color: white; }
        
        .actions {
            text-align: center;
            margin-top: 3rem;
        }
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background: #3b82f6;
            color: white;
            text-decoration: none;
            border-radius: 0.5rem;
            font-weight: bold;
            margin: 0 0.5rem;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #1e40af;
        }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 IPTV XUI One Content Manager</h1>
            <h2>System Check</h2>
            <div class="status-badge status-<?= $systemCheck['overall_status'] ?>">
                Status: <?= ucfirst($systemCheck['overall_status']) ?>
            </div>
        </div>

        <div class="grid">
            <!-- PHP Configuration -->
            <div class="card">
                <h3>🐘 PHP Configuration</h3>
                <div class="check-item">
                    <span>PHP Version</span>
                    <span class="check-status <?= $systemCheck['php']['version_ok'] ? 'status-ok' : 'status-error' ?>">
                        <?= $systemCheck['php']['version'] ?>
                    </span>
                </div>
                <div class="check-item">
                    <span>Memory Limit</span>
                    <span><?= $systemCheck['php']['memory_limit'] ?></span>
                </div>
                <div class="check-item">
                    <span>Upload Max Size</span>
                    <span><?= $systemCheck['php']['upload_max_filesize'] ?></span>
                </div>
                <div class="check-item">
                    <span>Max Execution Time</span>
                    <span><?= $systemCheck['php']['max_execution_time'] ?>s</span>
                </div>
            </div>

            <!-- PHP Extensions -->
            <div class="card">
                <h3>🔧 PHP Extensions</h3>
                <?php foreach ($systemCheck['extensions'] as $ext => $loaded): ?>
                <div class="check-item">
                    <span><?= strtoupper($ext) ?></span>
                    <span class="check-status <?= $loaded ? 'status-ok' : 'status-error' ?>">
                        <?= $loaded ? 'OK' : 'MISSING' ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- File Permissions -->
            <div class="card">
                <h3>📁 File Permissions</h3>
                <?php foreach ($systemCheck['permissions'] as $name => $perm): ?>
                <div class="check-item">
                    <span><?= ucfirst($name) ?></span>
                    <span class="check-status <?= $perm['writable'] ? 'status-ok' : 'status-error' ?>">
                        <?= $perm['writable'] ? 'WRITABLE' : 'NOT WRITABLE' ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Configuration Files -->
            <div class="card">
                <h3>⚙️ Configuration</h3>
                <?php foreach ($systemCheck['configuration'] as $name => $config): ?>
                <div class="check-item">
                    <span><?= $name ?></span>
                    <span class="check-status <?= $config['exists'] && $config['readable'] ? 'status-ok' : 'status-error' ?>">
                        <?= $config['exists'] && $config['readable'] ? 'OK' : 'MISSING' ?>
                    </span>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- Database Connection -->
            <div class="card">
                <h3>🗄️ Database</h3>
                <div class="check-item">
                    <span>Connection</span>
                    <span class="check-status <?= $systemCheck['database']['connection'] ? 'status-ok' : 'status-error' ?>">
                        <?= $systemCheck['database']['connection'] ? 'CONNECTED' : 'FAILED' ?>
                    </span>
                </div>
                <?php if ($systemCheck['database']['error']): ?>
                <div style="margin-top: 1rem; padding: 1rem; background: rgba(239, 68, 68, 0.1); border-radius: 0.5rem; color: #ef4444;">
                    <strong>Error:</strong> <?= htmlspecialchars($systemCheck['database']['error']) ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Hosting Information -->
            <div class="card">
                <h3>🌐 Hosting Info</h3>
                <div class="check-item">
                    <span>Environment</span>
                    <span><?= HOSTING_CONFIG['is_shared_hosting'] ? 'Shared Hosting' : 'Dedicated/VPS' ?></span>
                </div>
                <div class="check-item">
                    <span>Base URL</span>
                    <span><?= HOSTING_CONFIG['base_url'] ?></span>
                </div>
                <div class="check-item">
                    <span>Subdirectory</span>
                    <span><?= HOSTING_CONFIG['is_subdirectory'] ? 'Yes' : 'No' ?></span>
                </div>
            </div>
        </div>

        <div class="actions">
            <?php if ($systemCheck['overall_status'] === 'excellent' || $systemCheck['overall_status'] === 'good'): ?>
                <a href="index.php" class="btn">🚀 Launch Application</a>
            <?php endif; ?>
            <a href="?refresh=1" class="btn btn-secondary">🔄 Refresh Check</a>
        </div>

        <?php if ($systemCheck['overall_status'] === 'poor' || $systemCheck['overall_status'] === 'fair'): ?>
        <div style="margin-top: 2rem; padding: 2rem; background: rgba(239, 68, 68, 0.1); border-radius: 1rem; border: 1px solid #ef4444;">
            <h3 style="color: #ef4444; margin-top: 0;">⚠️ Issues Found</h3>
            <p>Please resolve the following issues before using the application:</p>
            <ul>
                <?php if (!$systemCheck['php']['version_ok']): ?>
                <li>PHP version 8.0 or higher is required</li>
                <?php endif; ?>
                
                <?php foreach ($systemCheck['extensions'] as $ext => $loaded): ?>
                    <?php if (!$loaded): ?>
                    <li>PHP extension "<?= $ext ?>" is missing</li>
                    <?php endif; ?>
                <?php endforeach; ?>
                
                <?php foreach ($systemCheck['permissions'] as $name => $perm): ?>
                    <?php if (!$perm['writable']): ?>
                    <li>Directory "<?= $name ?>" is not writable</li>
                    <?php endif; ?>
                <?php endforeach; ?>
                
                <?php if (!$systemCheck['database']['connection']): ?>
                <li>Database connection failed - check your configuration</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
