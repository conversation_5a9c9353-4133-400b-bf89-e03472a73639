<?php
/**
 * Dashboard Page - IPTV XUI One Content Manager
 * ============================================
 *
 * Main dashboard with statistics, charts, and quick actions
 */

// Session is already initialized by parent
// Include core classes
require_once __DIR__ . '/../../core/SimpleDatabaseManager.php';

try {
    $db = new SimpleDatabaseManager();
    
    // Test connection
    if (!$db->testConnection()) {
        throw new Exception("Database connection failed");
    }
    
    // Get statistics
    $contentStats = $db->getContentStats();
    $qualityStats = $db->getQualityStats();
    $recentContent = $db->getRecentAdditions(5);
    $popularContent = $db->getPopularContent(5);
    
} catch (Exception $e) {
    $error = $e->getMessage();
    $contentStats = [];
    $qualityStats = [];
    $recentContent = [];
    $popularContent = [];
}
?>

<style>
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

.stat-card {
    background: linear-gradient(135deg, var(--dark-surface), var(--dark-bg));
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.stat-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-change {
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.chart-container {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.chart-canvas {
    max-height: 400px;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.content-list {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 2rem;
}

.content-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--dark-border);
}

.content-list-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.content-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--dark-border);
    transition: var(--transition);
}

.content-item:last-child {
    border-bottom: none;
}

.content-item:hover {
    background: rgba(59, 130, 246, 0.05);
    border-radius: 0.5rem;
    margin: 0 -0.5rem;
    padding: 1rem 0.5rem;
}

.content-poster {
    width: 48px;
    height: 72px;
    background: var(--dark-border);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    font-size: 1.5rem;
}

.content-info {
    flex: 1;
}

.content-title {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.content-meta {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.action-card {
    background: var(--dark-surface);
    border: 1px solid var(--dark-border);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    transform: translateY(-2px);
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.2);
}

.action-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
}

.action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.action-description {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.error-message {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--error-color);
    border-radius: 0.5rem;
    padding: 1rem;
    color: var(--error-color);
    text-align: center;
    margin-bottom: 2rem;
}
</style>

<?php if (isset($error)): ?>
<div class="error-message">
    <i class="fas fa-exclamation-triangle"></i>
    <strong>Database Connection Error:</strong> <?= htmlspecialchars($error) ?>
    <br><small>Please check your database configuration in config/database.php</small>
</div>
<?php endif; ?>

<!-- Quick Actions -->
<div class="quick-actions">
    <a href="?page=m3u-manager" class="action-card">
        <div class="action-icon">
            <i class="fas fa-upload"></i>
        </div>
        <div class="action-title">Upload M3U</div>
        <div class="action-description">Import new content from M3U files</div>
    </a>
    
    <a href="?page=content-browser" class="action-card">
        <div class="action-icon">
            <i class="fas fa-search"></i>
        </div>
        <div class="action-title">Browse Content</div>
        <div class="action-description">Explore and manage your content</div>
    </a>
    
    <a href="?page=analytics" class="action-card">
        <div class="action-icon">
            <i class="fas fa-chart-bar"></i>
        </div>
        <div class="action-title">View Analytics</div>
        <div class="action-description">Detailed content statistics</div>
    </a>
    
    <a href="#" class="action-card" onclick="enrichTMDBContent()">
        <div class="action-icon">
            <i class="fas fa-magic"></i>
        </div>
        <div class="action-title">Enrich TMDB</div>
        <div class="action-description">Add missing metadata</div>
    </a>
</div>

<!-- Statistics Cards -->
<div class="dashboard-grid">
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Total Streams</div>
            <div class="stat-icon" style="background: rgba(59, 130, 246, 0.1); color: var(--primary-color);">
                <i class="fas fa-film"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['total_streams'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>All content types</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">Movies</div>
            <div class="stat-icon" style="background: rgba(16, 185, 129, 0.1); color: var(--success-color);">
                <i class="fas fa-video"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($contentStats['movies'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-link"></i>
            <span><?= number_format($contentStats['symlink_movies'] ?? 0) ?> symlink</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">4K Content</div>
            <div class="stat-icon" style="background: rgba(245, 158, 11, 0.1); color: var(--warning-color);">
                <i class="fas fa-crown"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_4k'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-shield-alt"></i>
            <span>Protected content</span>
        </div>
    </div>
    
    <div class="stat-card">
        <div class="stat-header">
            <div class="stat-title">60fps Content</div>
            <div class="stat-icon" style="background: rgba(139, 92, 246, 0.1); color: #8b5cf6;">
                <i class="fas fa-tachometer-alt"></i>
            </div>
        </div>
        <div class="stat-value"><?= number_format($qualityStats['content_60fps'] ?? 0) ?></div>
        <div class="stat-change positive">
            <i class="fas fa-star"></i>
            <span>High priority</span>
        </div>
    </div>
</div>

<!-- Charts -->
<div class="chart-container">
    <div class="chart-header">
        <div class="chart-title">Content Distribution</div>
        <div>
            <button class="btn btn-secondary" onclick="refreshCharts()">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
        </div>
    </div>
    <canvas id="contentChart" class="chart-canvas"></canvas>
</div>

<!-- Content Lists -->
<div class="content-grid">
    <div class="content-list">
        <div class="content-list-header">
            <div class="content-list-title">Recent Additions</div>
            <a href="?page=content-browser" class="btn btn-secondary">View All</a>
        </div>
        <?php if (empty($recentContent)): ?>
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-inbox" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No recent content found</p>
            </div>
        <?php else: ?>
            <?php foreach ($recentContent as $item): ?>
            <div class="content-item">
                <div class="content-poster">
                    <i class="fas fa-film"></i>
                </div>
                <div class="content-info">
                    <div class="content-title"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                    <div class="content-meta">
                        <?= $item['category_name'] ?? 'Uncategorized' ?> • 
                        <?= $item['year'] ?? 'Unknown year' ?> •
                        Rating: <?= number_format($item['rating'] ?? 0, 1) ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <div class="content-list">
        <div class="content-list-header">
            <div class="content-list-title">Popular Content</div>
            <a href="?page=analytics" class="btn btn-secondary">View Analytics</a>
        </div>
        <?php if (empty($popularContent)): ?>
            <div style="text-align: center; color: var(--text-secondary); padding: 2rem;">
                <i class="fas fa-star" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                <p>No popular content data</p>
            </div>
        <?php else: ?>
            <?php foreach ($popularContent as $item): ?>
            <div class="content-item">
                <div class="content-poster">
                    <i class="fas fa-star"></i>
                </div>
                <div class="content-info">
                    <div class="content-title"><?= htmlspecialchars($item['stream_display_name']) ?></div>
                    <div class="content-meta">
                        <?= $item['category_name'] ?? 'Uncategorized' ?> • 
                        Rating: <?= number_format($item['rating'] ?? 0, 1) ?>/10 •
                        <?= $item['year'] ?? 'Unknown year' ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<script>
// Initialize charts
document.addEventListener('DOMContentLoaded', function() {
    initializeContentChart();
});

function initializeContentChart() {
    const ctx = document.getElementById('contentChart').getContext('2d');
    
    const data = {
        labels: ['Movies', 'TV Shows', 'Live TV', 'Symlink', 'Direct Source'],
        datasets: [{
            data: [
                <?= $contentStats['movies'] ?? 0 ?>,
                <?= $contentStats['series'] ?? 0 ?>,
                <?= $contentStats['live_tv'] ?? 0 ?>,
                <?= $contentStats['symlink_movies'] ?? 0 ?>,
                <?= $contentStats['direct_movies'] ?? 0 ?>
            ],
            backgroundColor: [
                '#3b82f6',
                '#10b981',
                '#f59e0b',
                '#8b5cf6',
                '#ef4444'
            ],
            borderWidth: 0
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        color: '#94a3b8',
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

function refreshCharts() {
    showNotification('Refreshing charts...', 'info');
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function enrichTMDBContent() {
    if (confirm('This will enrich missing TMDB metadata. Continue?')) {
        showNotification('Starting TMDB enrichment...', 'info');
        // TODO: Implement TMDB enrichment via AJAX
    }
}
</script>
