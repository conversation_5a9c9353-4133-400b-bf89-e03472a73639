<?php
/**
 * Hosting Configuration for IPTV XUI One Content Manager
 * =====================================================
 * 
 * Auto-detection and configuration for various hosting environments
 */

// Auto-detect hosting environment
function detectHostingEnvironment() {
    $environment = [
        'is_shared_hosting' => false,
        'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? '',
        'script_path' => $_SERVER['SCRIPT_FILENAME'] ?? '',
        'base_url' => '',
        'is_subdirectory' => false
    ];
    
    // Detect if we're in a subdirectory
    $scriptDir = dirname($_SERVER['SCRIPT_NAME'] ?? '');
    if ($scriptDir !== '/') {
        $environment['is_subdirectory'] = true;
        $environment['base_url'] = $scriptDir;
    }
    
    // Detect shared hosting indicators
    $sharedHostingIndicators = [
        'public_html',
        'www',
        'htdocs',
        'web',
        'domains'
    ];
    
    foreach ($sharedHostingIndicators as $indicator) {
        if (strpos($environment['document_root'], $indicator) !== false) {
            $environment['is_shared_hosting'] = true;
            break;
        }
    }
    
    // Detect common hosting providers
    $serverSoftware = $_SERVER['SERVER_SOFTWARE'] ?? '';
    $serverName = $_SERVER['SERVER_NAME'] ?? '';
    
    if (strpos($serverSoftware, 'cPanel') !== false) {
        $environment['hosting_type'] = 'cpanel';
    } elseif (strpos($serverName, 'hostgator') !== false) {
        $environment['hosting_type'] = 'hostgator';
    } elseif (strpos($serverName, 'godaddy') !== false) {
        $environment['hosting_type'] = 'godaddy';
    } elseif (strpos($serverName, 'bluehost') !== false) {
        $environment['hosting_type'] = 'bluehost';
    } else {
        $environment['hosting_type'] = 'generic';
    }
    
    return $environment;
}

// Get the correct base path for includes
function getBasePath() {
    $currentDir = __DIR__;
    $projectRoot = dirname($currentDir);
    
    // If we're being called from the root index.php
    if (basename(dirname($_SERVER['SCRIPT_FILENAME'])) !== 'public') {
        return dirname($_SERVER['SCRIPT_FILENAME']);
    }
    
    return $projectRoot;
}

// Get the correct URL base for assets and links
function getUrlBase() {
    $environment = detectHostingEnvironment();
    
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
    
    $baseUrl = $protocol . $host;
    
    if ($environment['is_subdirectory']) {
        $baseUrl .= $environment['base_url'];
    }
    
    return rtrim($baseUrl, '/');
}

// Get asset URL (for CSS, JS, images)
function getAssetUrl($path) {
    $baseUrl = getUrlBase();
    return $baseUrl . '/assets/' . ltrim($path, '/');
}

// Get API URL
function getApiUrl($endpoint = '') {
    $baseUrl = getUrlBase();
    return $baseUrl . '/api/' . ltrim($endpoint, '/');
}

// Check if we can write to directories
function checkWritePermissions() {
    $basePath = getBasePath();
    $directories = [
        'logs' => $basePath . '/logs',
        'uploads' => $basePath . '/uploads',
        'uploads/temp' => $basePath . '/uploads/temp'
    ];
    
    $permissions = [];
    
    foreach ($directories as $name => $path) {
        if (!is_dir($path)) {
            @mkdir($path, 0755, true);
        }
        
        $permissions[$name] = [
            'exists' => is_dir($path),
            'writable' => is_writable($path),
            'path' => $path
        ];
    }
    
    return $permissions;
}

// Get PHP configuration for hosting
function getPhpConfig() {
    return [
        'version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'max_execution_time' => ini_get('max_execution_time'),
        'extensions' => [
            'pdo' => extension_loaded('pdo'),
            'pdo_mysql' => extension_loaded('pdo_mysql'),
            'json' => extension_loaded('json'),
            'mbstring' => extension_loaded('mbstring'),
            'curl' => extension_loaded('curl'),
            'fileinfo' => extension_loaded('fileinfo')
        ]
    ];
}

// Create necessary directories
function createDirectories() {
    $basePath = getBasePath();
    $directories = [
        '/logs',
        '/uploads',
        '/uploads/temp'
    ];
    
    $created = [];
    
    foreach ($directories as $dir) {
        $fullPath = $basePath . $dir;
        if (!is_dir($fullPath)) {
            $success = @mkdir($fullPath, 0755, true);
            $created[$dir] = $success;
        } else {
            $created[$dir] = true;
        }
    }
    
    return $created;
}

// Generate .htaccess for subdirectory if needed
function generateHtaccess() {
    $environment = detectHostingEnvironment();
    $basePath = getBasePath();
    $htaccessPath = $basePath . '/.htaccess';
    
    if ($environment['is_subdirectory'] && !file_exists($htaccessPath)) {
        $baseDir = $environment['base_url'];
        
        $htaccessContent = "# Auto-generated .htaccess for subdirectory installation
RewriteEngine On
RewriteBase {$baseDir}/

# API Routes
RewriteRule ^api/(.*)$ api/$1 [L,QSA]

# Asset Routes
RewriteRule ^assets/(.*)$ public/assets/$1 [L,QSA]

# Main Application
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [L,QSA]
";
        
        return file_put_contents($htaccessPath, $htaccessContent);
    }
    
    return true;
}

// Initialize hosting environment
function initializeHosting() {
    $environment = detectHostingEnvironment();
    $phpConfig = getPhpConfig();
    $permissions = checkWritePermissions();
    $directories = createDirectories();
    
    // Generate .htaccess if needed
    generateHtaccess();
    
    return [
        'environment' => $environment,
        'php_config' => $phpConfig,
        'permissions' => $permissions,
        'directories' => $directories,
        'base_path' => getBasePath(),
        'base_url' => getUrlBase()
    ];
}

// Define hosting configuration constants
$hostingInfo = initializeHosting();

define('HOSTING_CONFIG', [
    'environment' => $hostingInfo['environment'],
    'base_path' => $hostingInfo['base_path'],
    'base_url' => $hostingInfo['base_url'],
    'is_shared_hosting' => $hostingInfo['environment']['is_shared_hosting'],
    'is_subdirectory' => $hostingInfo['environment']['is_subdirectory']
]);

// Helper functions for templates
function asset_url($path) {
    return getAssetUrl($path);
}

function api_url($endpoint = '') {
    return getApiUrl($endpoint);
}

function base_url($path = '') {
    return getUrlBase() . '/' . ltrim($path, '/');
}
