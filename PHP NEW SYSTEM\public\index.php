<?php
/**
 * Main Entry Point for IPTV XUI One Content Manager
 * ================================================
 * 
 * Modern web interface for IPTV content management
 */

// Error reporting for development
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include path management first
require_once __DIR__ . '/../config/paths.php';

// Initialize session management
require_once getConfigPath('session.php');

// Include configuration
require_once getConfigPath('settings.php');
require_once getConfigPath('database.php');

// Simple routing
$page = $_GET['page'] ?? 'dashboard';
$validPages = ['dashboard', 'content-browser', 'm3u-manager', 'analytics', 'settings'];

if (!in_array($page, $validPages)) {
    $page = 'dashboard';
}

// Set page title
$pageTitle = ucfirst(str_replace('-', ' ', $page));
?>
<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $pageTitle ?> - IPTV XUI One Content Manager</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/themes.css">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom CSS for animations -->
    <style>
        :root {
            --primary-color: #3b82f6;
            --secondary-color: #1e40af;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --dark-bg: #0f172a;
            --dark-surface: #1e293b;
            --dark-border: #334155;
            --light-bg: #ffffff;
            --light-surface: #f8fafc;
            --light-border: #e2e8f0;
            --text-primary: #f1f5f9;
            --text-secondary: #94a3b8;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-bg);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        .app-container {
            display: grid;
            grid-template-columns: 280px 1fr;
            grid-template-rows: auto 1fr;
            min-height: 100vh;
            transition: var(--transition);
        }

        .sidebar {
            background: var(--dark-surface);
            border-right: 1px solid var(--dark-border);
            padding: 2rem 0;
            position: fixed;
            height: 100vh;
            width: 280px;
            z-index: 100;
            transform: translateX(0);
            transition: var(--transition);
        }

        .sidebar.collapsed {
            transform: translateX(-280px);
        }

        .sidebar-header {
            padding: 0 2rem 2rem;
            border-bottom: 1px solid var(--dark-border);
            margin-bottom: 2rem;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .logo i {
            font-size: 2rem;
        }

        .nav-menu {
            list-style: none;
            padding: 0 1rem;
        }

        .nav-item {
            margin-bottom: 0.5rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 0.75rem;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover {
            background: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 280px;
            padding: 2rem;
            transition: var(--transition);
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        .header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 1.5rem 2rem;
            background: var(--dark-surface);
            border-radius: 1rem;
            border: 1px solid var(--dark-border);
        }

        .header-title {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
        }

        .btn-secondary {
            background: var(--dark-border);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--dark-surface);
        }

        .theme-toggle {
            background: none;
            border: 1px solid var(--dark-border);
            color: var(--text-secondary);
            padding: 0.75rem;
            border-radius: 0.5rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .theme-toggle:hover {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .content-area {
            background: var(--dark-surface);
            border-radius: 1rem;
            border: 1px solid var(--dark-border);
            padding: 2rem;
            min-height: 600px;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--dark-border);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .app-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 2rem;
            right: 2rem;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(100%);
            transition: var(--transition);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success { background: var(--success-color); }
        .notification.error { background: var(--error-color); }
        .notification.warning { background: var(--warning-color); }
        .notification.info { background: var(--primary-color); }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-tv"></i>
                    <span>IPTV Manager</span>
                </div>
            </div>
            
            <nav>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="?page=dashboard" class="nav-link <?= $page === 'dashboard' ? 'active' : '' ?>">
                            <i class="fas fa-chart-line"></i>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="?page=content-browser" class="nav-link <?= $page === 'content-browser' ? 'active' : '' ?>">
                            <i class="fas fa-film"></i>
                            <span>Content Browser</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="?page=m3u-manager" class="nav-link <?= $page === 'm3u-manager' ? 'active' : '' ?>">
                            <i class="fas fa-upload"></i>
                            <span>M3U Manager</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="?page=analytics" class="nav-link <?= $page === 'analytics' ? 'active' : '' ?>">
                            <i class="fas fa-chart-pie"></i>
                            <span>Analytics</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="?page=settings" class="nav-link <?= $page === 'settings' ? 'active' : '' ?>">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content" id="mainContent">
            <!-- Header -->
            <header class="header">
                <div>
                    <h1 class="header-title"><?= $pageTitle ?></h1>
                </div>
                <div class="header-actions">
                    <button class="theme-toggle" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="btn btn-secondary" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </header>

            <!-- Content Area -->
            <div class="content-area fade-in">
                <?php
                // Include the requested page
                $pagePath = getPublicPath("pages/{$page}.php");
                if (file_exists($pagePath)) {
                    include $pagePath;
                } else {
                    echo '<div class="loading"><div class="spinner"></div></div>';
                    echo '<p style="text-align: center; margin-top: 1rem;">Page not found. Creating ' . $page . '...</p>';
                }
                ?>
            </div>
        </main>
    </div>

    <!-- Notification Container -->
    <div id="notifications"></div>

    <!-- JavaScript -->
    <script src="assets/js/main.js"></script>
    <script>
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle
            const themeToggle = document.getElementById('themeToggle');
            const html = document.documentElement;
            
            themeToggle.addEventListener('click', function() {
                const currentTheme = html.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                html.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                
                const icon = themeToggle.querySelector('i');
                icon.className = newTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
            });

            // Load saved theme
            const savedTheme = localStorage.getItem('theme') || 'dark';
            html.setAttribute('data-theme', savedTheme);
            const icon = themeToggle.querySelector('i');
            icon.className = savedTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';

            // Sidebar toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                mainContent.classList.toggle('expanded');
            });

            // Show welcome notification
            showNotification('Welcome to IPTV XUI One Content Manager!', 'success');
        });

        // Notification system
        function showNotification(message, type = 'info', duration = 3000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.getElementById('notifications').appendChild(notification);
            
            // Show notification
            setTimeout(() => notification.classList.add('show'), 100);
            
            // Hide notification
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }

        // Global error handler
        window.addEventListener('error', function(e) {
            showNotification('An error occurred: ' + e.message, 'error');
        });
    </script>
</body>
</html>
